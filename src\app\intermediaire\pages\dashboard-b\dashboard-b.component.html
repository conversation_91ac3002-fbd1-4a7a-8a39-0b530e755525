<!-- ===== CONTAINER PRINCIPAL ULTRA-MODERNE ===== -->
<div class="ultra-modern-dashboard">

    <!-- ===== HEADER DASHBOARD ===== -->
    <div class="dashboard-header">
        <div class="header-content">
            <div class="title-section">
                <h1 class="dashboard-title">
                    <i class="pi pi-chart-line title-icon"></i>
                    Dashboard Intermédiaire
                </h1>
                <p class="dashboard-subtitle">
                    {{ getCurrentDate() }} • {{ getCurrentTime() }}
                </p>
            </div>

            <div class="header-actions">
                <button class="modern-action-btn refresh-btn" (click)="refreshData()" [disabled]="isLoading"
                    pTooltip="Actualiser les données" tooltipPosition="bottom">
                    <i class="pi pi-refresh" [class.pi-spin]="isLoading"></i>
                    <span>Actualiser</span>
                </button>

                <button class="modern-action-btn notification-btn" (click)="openNotificationDialog()"
                    pTooltip="Envoyer une notification" tooltipPosition="bottom">
                    <i class="pi pi-bell"></i>
                    <span>Notification</span>
                </button>
            </div>
        </div>
    </div>

    <!-- ===== LOADING OVERLAY ===== -->
    <div class="loading-overlay" *ngIf="isLoading">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p class="loading-text">Chargement des données...</p>
        </div>
    </div>

    <!-- ===== CONTENU PRINCIPAL ===== -->
    <div class="dashboard-content" [class.loading]="isLoading">

        <!-- ===== SECTION KPI CARDS ===== -->
        <div class="kpi-section">
            <h2 class="section-title">
                <i class="pi pi-chart-bar"></i>
                Indicateurs Clés de Performance
            </h2>

            <div class="kpi-grid">
                <!-- KPI Card 1: Ordres Non Exécutables -->
                <div class="kpi-card danger-card">
                    <div class="card-header">
                        <div class="icon-container">
                            <i class="pi pi-exclamation-triangle"></i>
                        </div>
                        
                    </div>
                    <div class="card-content">
                        <h3 class="kpi-label">Ordres Non Exécutables</h3>
                        <div class="kpi-value-container">
                            <span class="kpi-value">{{ formatNumber(ordresNonExecutables) }}</span>
                            <span class="kpi-unit">ordres</span>
                        </div>
                        <div class="kpi-subtitle">Nécessitent une attention</div>
                    </div>
                    <div class="card-footer">
                        <div class="progress-bar">
                            <div class="progress-fill danger-fill" [style.width.%]="(ordresNonExecutables / 100) * 100">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- KPI Card 2: Commission 2 Jours -->
                <div class="kpi-card success-card">
                    <div class="card-header">
                        <div class="icon-container">
                            <i class="pi pi-money-bill"></i>
                        </div>
            
                    </div>
                    <div class="card-content">
                        <h3 class="kpi-label">Commission 2 Jours</h3>
                        <div class="kpi-value-container">
                            <span class="kpi-value">{{ formatNumber(commissionDerniers2Jours) }}</span>
                            <span class="currency">TND</span>
                        </div>
                        <div class="kpi-subtitle">Revenus générés</div>
                    </div>
                    <div class="card-footer">
                        <div class="progress-bar">
                            <div class="progress-fill success-fill" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <!-- KPI Card 3: Ratio Exécution -->
                <div class="kpi-card info-card">
                    <div class="card-header">
                        <div class="icon-container">
                            <i class="pi pi-percentage"></i>
                        </div>
                        
                    </div>
                    <div class="card-content">
                        <h3 class="kpi-label">Ratio d'Exécution</h3>
                        <div class="kpi-value-container">
                            <span class="kpi-value">{{ formatPercentage(ratioExecutionSurValides) }}</span>
                        </div>
                        <div class="kpi-subtitle">Ordres validés vs exécutés</div>
                    </div>
                    <div class="card-footer">
                        <div class="progress-bar">
                            <div class="progress-fill info-fill" [style.width.%]="ratioExecutionSurValides"></div>
                        </div>
                    </div>
                </div>

                <!-- KPI Card 4: Croissance Volume -->
                <div class="kpi-card warning-card">
                    <div class="card-header">
                        <div class="icon-container">
                            <i class="pi pi-chart-line"></i>
                        </div>
                       
                    </div>
                    <div class="card-content">
                        <h3 class="kpi-label">Croissance Volume</h3>
                        <div class="kpi-value-container">
                            <span class="kpi-value">{{ formatPercentage(croissanceVolumeOperations) }}</span>
                        </div>
                        <div class="kpi-subtitle">Évolution des opérations</div>
                    </div>
                    <div class="card-footer">
                        <div class="progress-bar">
                            <div class="progress-fill warning-fill"
                                [style.width.%]="Math.abs(croissanceVolumeOperations)"></div>
                        </div>
                    </div>
                </div>

                <!-- KPI Card 5: Investisseurs Actifs -->
                <div class="kpi-card primary-card">
                    <div class="card-header">
                        <div class="icon-container">
                            <i class="pi pi-users"></i>
                        </div>
                      
                    </div>
                    <div class="card-content">
                        <h3 class="kpi-label">Investisseurs Actifs</h3>
                        <div class="kpi-value-container">
                            <span class="kpi-value">{{ formatNumber(investisseursActifsAujourdhui) }}</span>
                            <span class="kpi-unit">aujourd'hui</span>
                        </div>
                        <div class="kpi-subtitle">Utilisateurs connectés</div>
                    </div>
                    <div class="card-footer">
                        <div class="progress-bar">
                            <div class="progress-fill primary-fill" [style.width.%]="investisseursActifsAujourdhui"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ===== SECTION GRAPHIQUES ===== -->
        <div class="charts-section">
            <div class="charts-grid">

                <!-- Graphique Top Actions -->
                <div class="chart-container" *ngIf="topActionsChartData">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="pi pi-chart-pie"></i>
                            Top 5 Actions Échangées
                        </h3>
                        <div class="chart-actions">
                            <button class="chart-action-btn" pTooltip="Exporter">
                                <i class="pi pi-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="chart-content">
                        <p-chart type="doughnut" [data]="topActionsChartData" [options]="topActionsChartOptions"
                            width="400" height="300">
                        </p-chart>
                    </div>
                </div>

            </div>
        </div>

        <!-- ===== SECTION HISTORIQUE ===== -->
        <div class="historique-section" *ngIf="historiqueExecution.length > 0">
            <div class="section-header">
                <h2 class="section-title">
                    <i class="pi pi-history"></i>
                    Historique des Exécutions
                </h2>
                
            </div>

            <div class="table-container">
                <p-table [value]="historiqueExecution" [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                    currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} entrées"
                    [rowsPerPageOptions]="[5, 10, 20]" styleClass="modern-table" [responsive]="true">

                    <ng-template pTemplate="header">
                        <tr>
                            <th pSortableColumn="date">
                                Date
                                <p-sortIcon field="date"></p-sortIcon>
                            </th>
                            <th pSortableColumn="nbOrdres">
                                Nb Ordres
                                <p-sortIcon field="nbOrdres"></p-sortIcon>
                            </th>
                            <th pSortableColumn="montant">
                                Montant (TND)
                                <p-sortIcon field="montant"></p-sortIcon>
                            </th>
                            <th pSortableColumn="statut">
                                Statut
                                <p-sortIcon field="statut"></p-sortIcon>
                            </th>
                           
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-historique>
                        <tr>
                            <td>
                                <div class="date-cell">
                                    <i class="pi pi-calendar"></i>
                                    {{ historique.date | date:'dd/MM/yyyy' }}
                                </div>
                            </td>
                            <td>
                                <div class="number-cell">
                                    <span class="number-badge">{{ historique.nbOrdres }}</span>
                                </div>
                            </td>
                            <td>
                                <div class="amount-cell">
                                    <span class="amount-value">{{ formatCurrency(historique.montant) }}</span>
                                </div>
                            </td>
                            <td>
                                <p-tag [value]="historique.statut" [severity]="getStatutSeverity(historique.statut)">
                                </p-tag>
                            </td>
                            
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="emptymessage">
                        <tr>
                            <td colspan="5" class="empty-message">
                                <div class="empty-state">
                                    <i class="pi pi-info-circle"></i>
                                    <p>Aucun historique d'exécution disponible</p>
                                </div>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>
        </div>
    </div>
</div>

<!-- ===== DIALOG NOTIFICATION ===== -->
<p-dialog header="Envoyer une Notification" [(visible)]="showNotificationDialog" [modal]="true"
    [style]="{width: '500px'}" [draggable]="false" [resizable]="false" styleClass="modern-dialog">

    <div class="notification-form">
        <div class="form-group">
            <label for="destinataire" class="form-label">
                <i class="pi pi-user"></i>
                Destinataire (optionnel)
            </label>
            <input type="text" id="destinataire" pInputText [(ngModel)]="notificationRequest.destinataire"
                placeholder="Nom d'utilisateur ou email" class="form-input">
        </div>

        <div class="form-group">
            <label for="message" class="form-label">
                <i class="pi pi-comment"></i>
                Message *
            </label>
            <textarea id="message" pInputTextarea [(ngModel)]="notificationRequest.message"
                placeholder="Saisissez votre message..." rows="4" class="form-textarea" [autoResize]="true">
          </textarea>
        </div>
    </div>

    <ng-template pTemplate="footer">
        <div class="dialog-footer">
            <button type="button" pButton label="Annuler" icon="pi pi-times" class="p-button-text p-button-secondary"
                (click)="cancelNotification()">
            </button>
            <button type="button" pButton label="Envoyer" icon="pi pi-send" class="p-button-success"
                (click)="sendNotification()" [disabled]="!notificationRequest.message.trim()">
            </button>
        </div>
    </ng-template>
</p-dialog>