export interface HistoriqueExecutionDTO {
    operationId: string;
    date: string;
    nbOrdres: number;
    montant: number;
    statut: string;
}

export interface NotificationRequestDTO {
    message: string;
    destinataire?: string;
    type?: string;
}

export interface DashboardKpiDTO {
    topActionsEchangees: Array<{[key: string]: any}>;
    ordresNonExecutables: number;
    commissionDerniers2Jours: number;
    ratioExecutionSurValides: number;
    croissanceVolumeOperations: number;
    investisseursActifsAujourdhui: number;
}
