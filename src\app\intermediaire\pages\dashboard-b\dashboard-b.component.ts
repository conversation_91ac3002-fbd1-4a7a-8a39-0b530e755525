import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IntermediaireService } from '../../services/intermediaire.service';
import { HistoriqueExecutionDTO, NotificationRequestDTO, ActionDTO } from '../../models/dashboard';
import { ChartModule } from 'primeng/chart';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressBarModule } from 'primeng/progressbar';

@Component({
  selector: 'app-dashboard-b',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChartModule,
    CardModule,
    ButtonModule,
    InputTextModule,
    InputTextareaModule,
    DialogModule,
    TableModule,
    TagModule,
    TooltipModule,
    ProgressBarModule
  ],
  templateUrl: './dashboard-b.component.html',
  styleUrl: './dashboard-b.component.css'
})
export class DashboardBComponent implements OnInit {

  // ===== DONNÉES DASHBOARD ===== //
  topActionsEchangees: Array<{ [key: string]: any }> = [];
  ordresNonExecutables: number = 0;
  commissionDerniers2Jours: number = 0;
  ratioExecutionSurValides: number = 0;
  croissanceVolumeOperations: number = 0;
  investisseursActifsAujourdhui: number = 0;
  kpiDashboard: { [key: string]: any } = {};
  historiqueExecution: HistoriqueExecutionDTO[] = [];
  actions: ActionDTO[] = [];

  // ===== ÉTATS DE CHARGEMENT ===== //
  isLoading: boolean = true;
  isLoadingKpi: boolean = false;
  isLoadingHistorique: boolean = false;

  // ===== DONNÉES GRAPHIQUES ===== //
  topActionsChartData: any;
  topActionsChartOptions: any;
  evolutionChartData: any;
  evolutionChartOptions: any;

  // ===== NOTIFICATION ===== //
  showNotificationDialog: boolean = false;
  notificationRequest: NotificationRequestDTO = {
    message: '',
    destinataire: '',
    type: 'INFO'
  };

  // ===== COULEURS MODERNES ===== //
  readonly modernColors = [
    '#0d542c', '#118749', '#1ea55c', '#2bb673', '#38c78a',
    '#45d8a1', '#52e9b8', '#5ffacf', '#6cffe6', '#79fffd'
  ];

  // Exposer Math pour le template
  Math = Math;

  constructor(private intermediaireService: IntermediaireService) { }

  ngOnInit(): void {
    this.loadAllDashboardData();
    this.initializeChartOptions();
  }

  // ===== CHARGEMENT DES DONNÉES ===== //
  loadAllDashboardData(): void {
    this.isLoading = true;

    // Charger tous les KPI en parallèle
    Promise.all([
      this.loadTopActionsEchangees(),
      this.loadOrdresNonExecutables(),
      this.loadCommissionDerniers2Jours(),
      this.loadRatioExecutionSurValides(),
      this.loadCroissanceVolumeOperations(),
      this.loadInvestisseursActifsAujourdhui(),
      this.loadKpiDashboard(),
      this.loadHistoriqueExecution(),
      this.loadActions()
    ]).finally(() => {
      this.isLoading = false;
      this.setupCharts();
    });
  }

  private loadTopActionsEchangees(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getTop5ActionsEchangees().subscribe({
        next: (data) => {
          this.topActionsEchangees = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement top actions:', err);
          resolve();
        }
      });
    });
  }

  private loadOrdresNonExecutables(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getOrdresNonExecutables().subscribe({
        next: (data) => {
          this.ordresNonExecutables = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement ordres non exécutables:', err);
          resolve();
        }
      });
    });
  }

  private loadCommissionDerniers2Jours(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getCommissionDerniers2Jours().subscribe({
        next: (data) => {
          this.commissionDerniers2Jours = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement commission:', err);
          resolve();
        }
      });
    });
  }

  private loadRatioExecutionSurValides(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getRatioExecutionSurValides().subscribe({
        next: (data) => {
          this.ratioExecutionSurValides = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement ratio exécution:', err);
          resolve();
        }
      });
    });
  }

  private loadCroissanceVolumeOperations(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getCroissanceVolumeOperations().subscribe({
        next: (data) => {
          this.croissanceVolumeOperations = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement croissance volume:', err);
          resolve();
        }
      });
    });
  }

  private loadInvestisseursActifsAujourdhui(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getInvestisseursActifsAujourdhui().subscribe({
        next: (data) => {
          this.investisseursActifsAujourdhui = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement investisseurs actifs:', err);
          resolve();
        }
      });
    });
  }

  private loadKpiDashboard(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getKpiDashboard().subscribe({
        next: (data) => {
          this.kpiDashboard = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement KPI dashboard:', err);
          resolve();
        }
      });
    });
  }

  private loadHistoriqueExecution(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getHistoriqueExecution().subscribe({
        next: (data) => {
          this.historiqueExecution = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement historique:', err);
          resolve();
        }
      });
    });
  }

  private loadActions(): Promise<void> {
    return new Promise((resolve) => {
      this.intermediaireService.getAllActions().subscribe({
        next: (data) => {
          this.actions = data;
          resolve();
        },
        error: (err) => {
          console.error('Erreur chargement actions:', err);
          resolve();
        }
      });
    });
  }

  // ===== CONFIGURATION DES GRAPHIQUES ===== //
  initializeChartOptions(): void {
    this.topActionsChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            color: '#495057',
            font: {
              family: 'Inter, sans-serif',
              size: 12
            }
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#0d542c',
          borderWidth: 1
        }
      }
    };

    this.evolutionChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          ticks: {
            color: '#6c757d',
            font: {
              family: 'Inter, sans-serif'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        },
        y: {
          ticks: {
            color: '#6c757d',
            font: {
              family: 'Inter, sans-serif'
            }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      plugins: {
        legend: {
          labels: {
            color: '#495057',
            font: {
              family: 'Inter, sans-serif'
            }
          }
        }
      }
    };
  }

  setupCharts(): void {
    // Configuration du graphique des top actions
    if (this.topActionsEchangees.length > 0) {
      const labels = this.topActionsEchangees.map(action => action['nom'] || action['symbole'] || 'Action');
      const data = this.topActionsEchangees.map(action => action['volume'] || action['quantite'] || 0);

      this.topActionsChartData = {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: this.modernColors.slice(0, data.length),
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      };
    }

    // Configuration du graphique d'évolution
    if (this.historiqueExecution.length > 0) {
      const labels = this.historiqueExecution.map(h => new Date(h.date).toLocaleDateString('fr-FR'));
      const montants = this.historiqueExecution.map(h => h.montant);
      const nbOrdres = this.historiqueExecution.map(h => h.nbOrdres);

      this.evolutionChartData = {
        labels: labels,
        datasets: [
          {
            label: 'Montant (TND)',
            data: montants,
            borderColor: '#0d542c',
            backgroundColor: 'rgba(13, 84, 44, 0.1)',
            tension: 0.4,
            fill: true
          },
          {
            label: 'Nombre d\'ordres',
            data: nbOrdres,
            borderColor: '#118749',
            backgroundColor: 'rgba(17, 135, 73, 0.1)',
            tension: 0.4,
            fill: true,
            yAxisID: 'y1'
          }
        ]
      };
    }
  }

  // ===== MÉTHODES UTILITAIRES ===== //
  getCurrentTime(): string {
    return new Date().toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('fr-FR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(value);
  }

  formatCurrency(value: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'TND',
      minimumFractionDigits: 2
    }).format(value);
  }

  formatPercentage(value: number): string {
    return new Intl.NumberFormat('fr-FR', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 2
    }).format(value / 100);
  }

  getStatutSeverity(statut: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" | undefined {
    switch (statut?.toLowerCase()) {
      case 'execute':
      case 'exécuté':
        return 'success';
      case 'en_cours':
      case 'en cours':
        return 'warning';
      case 'annule':
      case 'annulé':
        return 'danger';
      default:
        return 'info';
    }
  }

  getTrendIcon(value: number): string {
    if (value > 0) return 'pi-arrow-up';
    if (value < 0) return 'pi-arrow-down';
    return 'pi-minus';
  }

  getTrendClass(value: number): string {
    if (value > 0) return 'trend-positive';
    if (value < 0) return 'trend-negative';
    return 'trend-neutral';
  }

  // ===== ACTIONS ===== //
  refreshData(): void {
    this.loadAllDashboardData();
  }

  openNotificationDialog(): void {
    this.showNotificationDialog = true;
    this.notificationRequest = {
      message: '',
      destinataire: '',
      type: 'INFO'
    };
  }

  sendNotification(): void {
    if (this.notificationRequest.message.trim()) {
      this.intermediaireService.envoyerNotification(this.notificationRequest).subscribe({
        next: (response) => {
          console.log('Notification envoyée:', response);
          this.showNotificationDialog = false;
        },
        error: (err) => {
          console.error('Erreur envoi notification:', err);
        }
      });
    }
  }

  cancelNotification(): void {
    this.showNotificationDialog = false;
  }

  // ===== MÉTHODES POUR LES ACTIONS ===== //
  getSecteurIcon(secteur: string): string {
    switch (secteur?.toLowerCase()) {
      case 'technologie': return 'pi pi-desktop';
      case 'finance': return 'pi pi-wallet';
      case 'industrie': return 'pi pi-cog';
      case 'e-commerce': return 'pi pi-shopping-cart';
      case 'banque': return 'pi pi-building';
      case 'assurance': return 'pi pi-shield';
      case 'immobilier': return 'pi pi-home';
      case 'energie': return 'pi pi-bolt';
      case 'telecommunications': return 'pi pi-phone';
      case 'sante': return 'pi pi-heart';
      default: return 'pi pi-briefcase';
    }
  }

  getSectorClass(secteur: string): string {
    switch (secteur?.toLowerCase()) {
      case 'technologie': return 'sector-tech';
      case 'finance': return 'sector-finance';
      case 'industrie': return 'sector-industry';
      case 'e-commerce': return 'sector-ecommerce';
      case 'banque': return 'sector-bank';
      case 'assurance': return 'sector-insurance';
      case 'immobilier': return 'sector-real-estate';
      case 'energie': return 'sector-energy';
      case 'telecommunications': return 'sector-telecom';
      case 'sante': return 'sector-health';
      default: return 'sector-default';
    }
  }

  getRandomColorClass(nom: string): string {
    const colors = ['avatar-blue', 'avatar-green', 'avatar-purple', 'avatar-orange', 'avatar-red'];
    const index = nom.charCodeAt(0) % colors.length;
    return colors[index];
  }

  getPriceChangeClass(action: ActionDTO): string {
    // Simuler un changement de prix basé sur le nom de l'action
    const hash = action.nom.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return hash % 2 === 0 ? 'positive' : 'negative';
  }

  getPriceChangeValue(action: ActionDTO): string {
    // Simuler un pourcentage de changement
    const hash = action.nom.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    const change = (hash % 10) / 10;
    const sign = hash % 2 === 0 ? '+' : '-';
    return `${sign}${change.toFixed(1)}%`;
  }

  getPriceChangeIcon(action: ActionDTO): string {
    const hash = action.nom.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return hash % 2 === 0 ? 'pi pi-arrow-up' : 'pi pi-arrow-down';
  }

  // Méthodes pour les statistiques des actions
  getTechActionsCount(): number {
    return this.actions.filter(a => a.secteur === 'Technologie').length;
  }

  getFinanceActionsCount(): number {
    return this.actions.filter(a => a.secteur === 'Finance').length;
  }

}
