/* ===== DESIGN ULTRA-MODERNE DASHBOARD INTERMÉDIAIRE ===== */

/* Variables CSS pour la cohérence */
:root {
    --primary-color: #0d542c;
    --primary-light: #118749;
    --primary-dark: #0a3d20;
    --secondary-color: #f8f9fa;
    --accent-color: #e3f2fd;
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-light: #95a5a6;
    --border-color: #e9ecef;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.16);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #0d542c 0%, #118749 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-info: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* Container principal ultra-moderne */
.ultra-modern-dashboard {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    width: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    position: relative;
    overflow-x: hidden;
}

/* ===== HEADER DASHBOARD ===== */
.dashboard-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-light);
    padding: 2rem 2.5rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.title-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    -webkit-text-fill-color: var(--primary-color);
}

.dashboard-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.modern-action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
}

.modern-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.modern-action-btn:hover::before {
    left: 100%;
}

.modern-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-light);
}

.refresh-btn:hover {
    background: var(--gradient-primary);
    color: white;
}

.notification-btn:hover {
    background: var(--gradient-info);
    color: white;
}

.modern-action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.modern-action-btn i {
    font-size: 1.1rem;
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin: 0;
}

/* ===== CONTENU PRINCIPAL ===== */
.dashboard-content {
    padding: 2.5rem;
    max-width: 1400px;
    margin: 0 auto;
    transition: var(--transition);
}

.dashboard-content.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* ===== SECTION TITLES ===== */
.section-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 2rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.section-title i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

/* ===== KPI SECTION ===== */
.kpi-section {
    margin-bottom: 3rem;
}

.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

/* ===== KPI CARDS ===== */
.kpi-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.75rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transition: var(--transition);
}

.kpi-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.kpi-card:hover::before {
    height: 6px;
}

/* Variantes de couleurs pour les KPI cards */
.danger-card::before {
    background: var(--gradient-danger);
}

.success-card::before {
    background: var(--gradient-success);
}

.info-card::before {
    background: var(--gradient-info);
}

.warning-card::before {
    background: var(--gradient-warning);
}

.primary-card::before {
    background: var(--gradient-primary);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.icon-container {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(13, 84, 44, 0.1) 0%, rgba(17, 135, 73, 0.1) 100%);
    color: var(--primary-color);
    font-size: 1.5rem;
    transition: var(--transition);
}

.danger-card .icon-container {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
    color: #ef4444;
}

.success-card .icon-container {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
    color: #10b981;
}

.info-card .icon-container {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
    color: #3b82f6;
}

.warning-card .icon-container {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
    color: #f59e0b;
}

.kpi-card:hover .icon-container {
    transform: scale(1.1);
}

.trend-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    transition: var(--transition);
}

.trend-indicator.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.trend-indicator.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.trend-indicator.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.trend-positive {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
}

.trend-negative {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.trend-neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.card-content {
    margin-bottom: 1.5rem;
}

.kpi-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0 0 0.75rem 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.kpi-value-container {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.kpi-value {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--text-primary);
    line-height: 1;
}

.kpi-unit,
.currency {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.kpi-subtitle {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

.card-footer {
    margin-top: auto;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 3px;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

.primary-fill {
    background: var(--gradient-primary);
}

.success-fill {
    background: var(--gradient-success);
}

.warning-fill {
    background: var(--gradient-warning);
}

.danger-fill {
    background: var(--gradient-danger);
}

.info-fill {
    background: var(--gradient-info);
}

/* ===== SECTION GRAPHIQUES ===== */
.charts-section {
    margin-bottom: 3rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 1.5rem;
}

.chart-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.75rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chart-title i {
    color: var(--primary-color);
}

.chart-actions {
    display: flex;
    gap: 0.5rem;
}

.chart-action-btn {
    width: 40px;
    height: 40px;
    border: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.chart-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.chart-content {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

/* ===== SECTION HISTORIQUE ===== */
.historique-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
}

.export-btn {
    background: var(--gradient-success);
    color: white;
    border: none;
}

.export-btn:hover {
    background: var(--gradient-success);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.table-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    overflow: hidden;
}

/* ===== STYLES TABLE MODERNE ===== */
::ng-deep .modern-table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

::ng-deep .modern-table .p-datatable-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem;
}

::ng-deep .modern-table .p-datatable-thead>tr>th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

::ng-deep .modern-table .p-datatable-tbody>tr {
    transition: var(--transition);
}

::ng-deep .modern-table .p-datatable-tbody>tr:hover {
    background: rgba(13, 84, 44, 0.05);
    transform: scale(1.01);
}

::ng-deep .modern-table .p-datatable-tbody>tr>td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.date-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.date-cell i {
    color: var(--primary-color);
}

.number-cell {
    text-align: center;
}

.number-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
}

.amount-cell {
    text-align: right;
}

.amount-value {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.view-btn:hover {
    background: var(--gradient-info);
    color: white;
    transform: scale(1.1);
}

.download-btn:hover {
    background: var(--gradient-success);
    color: white;
    transform: scale(1.1);
}

.empty-message {
    text-align: center;
    padding: 3rem 1rem;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--text-light);
}

.empty-state i {
    font-size: 3rem;
    color: var(--text-light);
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* ===== DIALOG NOTIFICATION ===== */
::ng-deep .modern-dialog {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
}

::ng-deep .modern-dialog .p-dialog-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem;
    border: none;
}

::ng-deep .modern-dialog .p-dialog-content {
    padding: 2rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
}

::ng-deep .modern-dialog .p-dialog-footer {
    background: rgba(248, 250, 252, 0.95);
    border: none;
    padding: 1.5rem 2rem;
}

.notification-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
}

.form-label i {
    color: var(--primary-color);
}

.form-input,
.form-textarea {
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(13, 84, 44, 0.1);
}

.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* ===== ANIMATIONS SUPPLÉMENTAIRES ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.kpi-card {
    animation: fadeInUp 0.6s ease-out;
}

.kpi-card:nth-child(1) {
    animation-delay: 0.1s;
}

.kpi-card:nth-child(2) {
    animation-delay: 0.2s;
}

.kpi-card:nth-child(3) {
    animation-delay: 0.3s;
}

.kpi-card:nth-child(4) {
    animation-delay: 0.4s;
}

.kpi-card:nth-child(5) {
    animation-delay: 0.5s;
}

.chart-container:nth-child(1) {
    animation: slideInLeft 0.8s ease-out;
}

.chart-container:nth-child(2) {
    animation: slideInRight 0.8s ease-out;
}

/* ===== DESIGN RESPONSIF ===== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
    .ultra-modern-dashboard {
        max-width: 1400px;
        margin: 0 auto;
    }

    .dashboard-content {
        padding: 3rem;
    }

    .kpi-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) {
    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }

    .charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Tablette Large (992px - 1199px) */
@media (max-width: 1199px) {
    .dashboard-header {
        padding: 1.5rem 2rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .dashboard-content {
        padding: 2rem;
    }

    .kpi-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

/* Tablette (768px - 991px) */
@media (max-width: 991px) {
    .dashboard-title {
        font-size: 2rem;
    }

    .title-icon {
        font-size: 2rem;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
        gap: 0.75rem;
    }

    .modern-action-btn {
        width: 100%;
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .section-actions {
        width: 100%;
        justify-content: center;
    }
}

/* Mobile Large (576px - 767px) */
@media (max-width: 767px) {
    .dashboard-header {
        padding: 1rem;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .dashboard-title {
        font-size: 1.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .kpi-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .kpi-card {
        padding: 1.25rem;
    }

    .kpi-value {
        font-size: 1.875rem;
    }

    .chart-container {
        padding: 1rem;
    }

    .chart-content {
        min-height: 250px;
    }

    .table-container {
        padding: 1rem;
        overflow-x: auto;
    }

    ::ng-deep .modern-table .p-datatable-thead>tr>th,
    ::ng-deep .modern-table .p-datatable-tbody>tr>td {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        width: 32px;
        height: 32px;
    }
}

/* Mobile Small (≤575px) */
@media (max-width: 575px) {
    .dashboard-header {
        padding: 0.75rem;
    }

    .dashboard-content {
        padding: 0.75rem;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .title-icon {
        font-size: 1.5rem;
    }

    .kpi-card {
        padding: 1rem;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .icon-container {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .kpi-value {
        font-size: 1.5rem;
    }

    .chart-container {
        padding: 0.75rem;
    }

    .chart-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .chart-title {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    ::ng-deep .modern-dialog {
        width: 95vw !important;
        max-width: none !important;
    }

    .notification-form {
        gap: 1rem;
    }

    .dialog-footer {
        flex-direction: column;
        gap: 0.75rem;
    }

    .dialog-footer button {
        width: 100%;
    }
}

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .loading-spinner {
        animation: none;
    }

    .progress-fill::after {
        animation: none;
    }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #118749;
        --primary-light: #1ea55c;
        --primary-dark: #0d542c;
        --secondary-color: #2c3e50;
        --accent-color: #34495e;
        --text-primary: #ecf0f1;
        --text-secondary: #bdc3c7;
        --text-light: #95a5a6;
        --border-color: #34495e;
    }

    .ultra-modern-dashboard {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .dashboard-header {
        background: linear-gradient(135deg, rgba(44, 62, 80, 0.95) 0%, rgba(52, 73, 94, 0.95) 100%);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .kpi-card,
    .chart-container,
    .table-container {
        background: linear-gradient(135deg, rgba(44, 62, 80, 0.95) 0%, rgba(52, 73, 94, 0.95) 100%);
        border-color: rgba(255, 255, 255, 0.1);
    }
}